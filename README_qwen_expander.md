# 🤖 AI驱动的内容扩展工具 (Qwen版本)

这个工具使用Qwen大模型动态生成扩展内容，避免模板重复问题。

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install requests
```

### 2. 配置API密钥

**方法A: 环境变量（推荐）**
```bash
# Windows
set QWEN_API_KEY=your-actual-api-key

# Linux/Mac
export QWEN_API_KEY=your-actual-api-key
```

**方法B: 命令行参数**
```bash
python ai_powered_expander.py your_file.json --real-api --api-key your-actual-api-key
```

**方法C: 修改配置文件**
编辑 `qwen_config.py` 文件，设置 `QWEN_API_KEY`

### 3. 运行扩展

**模拟模式（测试用）**
```bash
python ai_powered_expander.py gemini_2.5pro_25_08_25_18.json
```

**真实API模式**
```bash
python ai_powered_expander.py gemini_2.5pro_25_08_25_18.json --real-api
```

**限制处理数量**
```bash
python ai_powered_expander.py gemini_2.5pro_25_08_25_18.json --real-api --max-items 5
```

## 🎯 工作原理

### 1. **智能分析**
- 分析每个条目的内容结构
- 识别缺少的元素（例子、结论、深入分析等）
- 计算需要扩展的精确字数

### 2. **动态生成**
- 根据问题主题和现有内容生成定制化提示词
- 调用Qwen API生成独特的扩展内容
- 实时监控字数，达到目标即停止

### 3. **质量保证**
- 多次重试机制
- 备份原文件
- 详细的进度报告

## 📊 示例输出

```
正在使用AI动态扩展内容...
API模式: Qwen API
已创建备份文件: gemini_2.5pro_25_08_25_18.json.ai_backup
发现 15 个需要扩展的条目

正在扩展 smp25_pre_055 (不足 52.3%, 需要 685 字)
  正在调用Qwen API生成扩展内容... (尝试 1/3)
  扩展后: 1194/1300 字 (不足 8.2%)

正在扩展 smp25_pre_047 (不足 48.7%, 需要 487 字)
  正在调用Qwen API生成扩展内容... (尝试 1/3)
  扩展后: 1018/1000 字 (不足 -1.8%)

AI动态扩展完成! 共扩展了 10 个条目
```

## ⚙️ 配置选项

在 `qwen_config.py` 中可以调整：

- **模型选择**: `qwen-max`（最强）, `qwen-plus`（平衡）, `qwen-turbo`（快速）
- **生成参数**: temperature, top_p, max_tokens
- **质量控制**: 重试次数、目标准确度等

## 🔄 与模板方案的对比

| 特性 | 模板方案 | Qwen API方案 |
|------|----------|--------------|
| 内容重复 | ❌ 高重复 | ✅ 零重复 |
| 相关性 | ⚠️ 通用性强 | ✅ 高度相关 |
| 成本 | ✅ 免费 | ⚠️ 需要API费用 |
| 速度 | ✅ 即时 | ⚠️ 需要网络调用 |
| 质量 | ⚠️ 固定质量 | ✅ 动态优化 |

## 💡 使用建议

1. **先用模拟模式测试**，确认逻辑正确
2. **小批量处理**，避免大量API调用
3. **检查生成质量**，必要时调整提示词
4. **备份文件**会自动创建，可以随时恢复

## 🛠️ 故障排除

- **API调用失败**: 检查网络连接和API密钥
- **内容质量不佳**: 调整temperature参数或提示词
- **字数仍不准确**: 可能需要多轮迭代优化
