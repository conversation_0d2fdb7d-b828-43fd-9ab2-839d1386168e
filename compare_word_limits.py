import json
import sys

def compare_word_limits(example_file, submission_file):
    """
    比较示例文件和提交文件中的字数限制是否一致
    """
    print("正在比较字数限制...")
    
    # 读取示例文件
    with open(example_file, 'r', encoding='utf-8') as f:
        example_data = json.load(f)
    
    # 读取提交文件
    with open(submission_file, 'r', encoding='utf-8') as f:
        submission_data = json.load(f)
    
    print(f"示例文件包含 {len(example_data)} 个条目")
    print(f"提交文件包含 {len(submission_data)} 个条目")
    
    # 创建字典以便快速查找
    example_dict = {item['id']: item for item in example_data}
    submission_dict = {item['id']: item for item in submission_data}
    
    # 检查字数限制是否一致
    mismatches = []
    for item_id in example_dict:
        if item_id in submission_dict:
            example_limit = example_dict[item_id]['word_limit']
            submission_limit = submission_dict[item_id]['word_limit']
            
            if example_limit != submission_limit:
                mismatches.append({
                    'id': item_id,
                    'example_limit': example_limit,
                    'submission_limit': submission_limit
                })
    
    if mismatches:
        print(f"\n发现 {len(mismatches)} 个字数限制不匹配的条目:")
        for mismatch in mismatches:
            print(f"  ID: {mismatch['id']}")
            print(f"    示例文件: {mismatch['example_limit']}")
            print(f"    提交文件: {mismatch['submission_limit']}")
    else:
        print("\n所有条目的字数限制都匹配!")
    
    # 检查是否有缺失的条目
    missing_in_submission = set(example_dict.keys()) - set(submission_dict.keys())
    missing_in_example = set(submission_dict.keys()) - set(example_dict.keys())
    
    if missing_in_submission:
        print(f"\n提交文件中缺失的条目: {missing_in_submission}")
    
    if missing_in_example:
        print(f"\n示例文件中没有的条目: {missing_in_example}")

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("用法: python compare_word_limits.py <example_file> <submission_file>")
        sys.exit(1)
    
    example_file = sys.argv[1]
    submission_file = sys.argv[2]
    compare_word_limits(example_file, submission_file)
