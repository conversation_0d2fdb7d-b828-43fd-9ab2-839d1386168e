import json
import sys
import shutil
import re

def count_words(text):
    """计算字数"""
    if not text:
        return 0
    return len(text.strip().split())

def generate_topic_specific_content(question, topic_type, current_content, words_needed):
    """
    根据问题和主题类型生成相关的扩展内容
    """
    
    # 分析当前内容的结构
    paragraphs = current_content.split('\n\n')
    has_introduction = len(paragraphs) > 0 and len(paragraphs[0]) > 100
    has_conclusion = 'conclusion' in current_content.lower() or 'in summary' in current_content.lower()
    
    expansions = []
    
    # 根据主题类型添加相关内容
    if 'AI' in topic_type or 'Tech' in topic_type:
        if words_needed > 100:
            expansions.append("The technological infrastructure supporting these developments involves sophisticated computational frameworks, advanced algorithms, and robust data processing capabilities that enable real-time analysis and decision-making. The integration of machine learning models with existing systems requires careful consideration of compatibility, scalability, and performance optimization to ensure seamless operation across diverse environments and use cases.")
        
        if words_needed > 200:
            expansions.append("Implementation challenges encompass both technical and organizational dimensions, including data quality and availability, system integration complexity, user adoption and training requirements, and the need for ongoing maintenance and updates. Organizations must develop comprehensive strategies that address these challenges while maximizing the benefits of technological innovation and ensuring sustainable long-term outcomes.")
            
    elif 'Health' in topic_type or 'Life Sciences' in topic_type:
        if words_needed > 100:
            expansions.append("The clinical implications of these developments extend beyond immediate therapeutic applications to encompass broader healthcare system considerations, including cost-effectiveness, accessibility, patient safety protocols, and the need for specialized training and infrastructure. Healthcare providers must carefully evaluate the evidence base, regulatory requirements, and practical implementation considerations when adopting new approaches.")
        
        if words_needed > 200:
            expansions.append("Patient outcomes and quality of life considerations remain paramount in evaluating the success of these interventions. Long-term follow-up studies, comparative effectiveness research, and real-world evidence generation are essential for understanding the true impact and optimizing treatment protocols. Additionally, ethical considerations regarding informed consent, equity of access, and the balance between benefits and risks must be carefully addressed.")
            
    elif 'Business' in topic_type or 'Market' in topic_type:
        if words_needed > 100:
            expansions.append("Market dynamics and competitive landscapes are continuously evolving, influenced by technological innovation, regulatory changes, consumer preferences, and global economic conditions. Organizations must develop adaptive strategies that can respond to these changes while maintaining competitive advantage and sustainable growth trajectories.")
        
        if words_needed > 200:
            expansions.append("Financial implications and investment considerations play a crucial role in determining the viability and scalability of these business models. Risk assessment, return on investment analysis, and strategic planning must account for both short-term operational requirements and long-term market positioning to ensure sustainable success in increasingly competitive environments.")
            
    elif 'Social' in topic_type or 'Cultural' in topic_type:
        if words_needed > 100:
            expansions.append("The sociological implications extend beyond immediate observable effects to encompass deeper questions about social cohesion, cultural identity, and the evolution of human relationships in contemporary society. These changes reflect broader transformations in how individuals and communities interact, communicate, and form meaningful connections in an increasingly interconnected world.")
        
        if words_needed > 200:
            expansions.append("Cultural adaptation and resistance patterns vary significantly across different demographic groups, geographic regions, and socioeconomic contexts. Understanding these variations is essential for developing effective interventions and policies that can address diverse needs while respecting cultural values and promoting inclusive outcomes for all members of society.")
            
    elif 'Environmental' in topic_type or 'Sustainability' in topic_type:
        if words_needed > 100:
            expansions.append("Environmental impact assessments and sustainability considerations are integral to evaluating the long-term viability of these approaches. Life cycle analysis, carbon footprint evaluation, and ecosystem impact studies provide essential data for understanding the full environmental implications and identifying opportunities for optimization and improvement.")
        
        if words_needed > 200:
            expansions.append("Policy frameworks and regulatory mechanisms play a crucial role in promoting sustainable practices while balancing economic development needs with environmental protection goals. International cooperation, stakeholder engagement, and adaptive management approaches are essential for addressing complex environmental challenges that transcend national boundaries and require coordinated global responses.")
    
    # 添加通用的分析性内容
    if words_needed > 150:
        expansions.append("Stakeholder perspectives and interests vary significantly, creating complex dynamics that must be carefully navigated to achieve successful outcomes. Effective engagement strategies require understanding the motivations, concerns, and constraints of different stakeholder groups while building consensus around shared objectives and collaborative approaches to problem-solving.")
    
    if words_needed > 250:
        expansions.append("International comparisons and best practice analysis reveal diverse approaches and varying levels of success across different contexts and implementations. These comparative insights provide valuable lessons for policy development, strategic planning, and implementation optimization, highlighting both successful strategies and common pitfalls to avoid.")
    
    # 如果没有结论，添加一个
    if not has_conclusion and words_needed > 50:
        expansions.append("In conclusion, addressing these complex challenges requires a multifaceted approach that integrates technological innovation, policy development, stakeholder engagement, and continuous evaluation and adaptation. Success depends on maintaining a balance between ambitious goals and practical constraints while fostering collaboration and innovation across all relevant sectors and communities.")
    
    return expansions

def smart_expand_content(file_path, target_improvement=0.15):
    """
    智能扩展内容，优先处理字数不足最严重的条目
    """
    print(f"正在智能扩展内容...")
    
    # 备份文件
    backup_file = file_path + '.smart_backup'
    shutil.copy2(file_path, backup_file)
    print(f"已创建备份文件: {backup_file}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 识别需要扩展的条目
    items_to_expand = []
    for i, item in enumerate(data):
        if item['answer'].strip():
            actual_words = count_words(item['answer'])
            required_words = item['word_limit']
            shortfall_percent = ((required_words - actual_words) / required_words) * 100
            
            if shortfall_percent > 15:  # 字数不足超过15%的条目
                items_to_expand.append({
                    'index': i,
                    'id': item['id'],
                    'shortfall_percent': shortfall_percent,
                    'actual_words': actual_words,
                    'required_words': required_words,
                    'words_needed': required_words - actual_words
                })
    
    # 按字数不足程度排序
    items_to_expand.sort(key=lambda x: x['shortfall_percent'], reverse=True)
    
    print(f"发现 {len(items_to_expand)} 个需要扩展的条目")
    
    # 扩展条目
    expanded_count = 0
    for item_info in items_to_expand:
        index = item_info['index']
        item = data[index]
        
        print(f"正在扩展 {item['id']} (不足 {item_info['shortfall_percent']:.1f}%, 需要 {item_info['words_needed']} 字)")
        
        # 生成扩展内容
        expansions = generate_topic_specific_content(
            item['question'], 
            item['type'], 
            item['answer'], 
            item_info['words_needed']
        )
        
        # 将扩展内容添加到原答案中
        expanded_answer = item['answer']
        for expansion in expansions:
            expanded_answer += "\n\n" + expansion
            
            # 检查是否已经达到目标字数
            current_word_count = count_words(expanded_answer)
            if current_word_count >= item['word_limit'] * 0.9:  # 达到90%即可
                break
        
        data[index]['answer'] = expanded_answer
        expanded_count += 1
        
        # 验证扩展效果
        new_word_count = count_words(expanded_answer)
        new_shortfall = ((item['word_limit'] - new_word_count) / item['word_limit']) * 100
        print(f"  扩展后: {new_word_count}/{item['word_limit']} 字 (不足 {new_shortfall:.1f}%)")
        
        # 如果处理了20个条目就暂停，避免一次性处理太多
        if expanded_count >= 20:
            print(f"已处理 {expanded_count} 个条目，暂停以检查效果...")
            break
    
    # 保存修改后的文件
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)
    
    print(f"\n智能扩展完成! 共扩展了 {expanded_count} 个条目")
    return expanded_count

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python smart_content_expander.py <json_file>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    expanded_count = smart_expand_content(file_path)
