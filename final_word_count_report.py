import json
import sys

def count_words(text):
    """计算字数"""
    if not text:
        return 0
    return len(text.strip().split())

def generate_final_report(file_path):
    """生成最终的字数检查报告"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("=" * 80)
    print("最终字数修复报告")
    print("=" * 80)
    print(f"文件: {file_path}")
    print(f"总条目数: {len(data)}")
    
    # 统计各种偏差范围的条目
    perfect_count = 0  # 偏差 <= 5%
    excellent_count = 0  # 偏差 <= 10%
    good_count = 0  # 偏差 <= 15%
    acceptable_count = 0  # 偏差 <= 20%
    needs_improvement = 0  # 偏差 > 20%
    
    total_deviation = 0
    problem_items = []
    
    for i, item in enumerate(data):
        if item['answer'].strip():
            actual_words = count_words(item['answer'])
            required_words = item['word_limit']
            deviation = abs(actual_words - required_words) / required_words * 100
            
            total_deviation += deviation
            
            if deviation <= 5:
                perfect_count += 1
            elif deviation <= 10:
                excellent_count += 1
            elif deviation <= 15:
                good_count += 1
            elif deviation <= 20:
                acceptable_count += 1
            else:
                needs_improvement += 1
                problem_items.append({
                    'id': item['id'],
                    'actual': actual_words,
                    'required': required_words,
                    'deviation': deviation
                })
    
    non_empty_count = len([item for item in data if item['answer'].strip()])
    avg_deviation = total_deviation / non_empty_count if non_empty_count > 0 else 0
    
    print(f"\n字数质量分布:")
    print(f"  完美 (偏差 ≤ 5%):     {perfect_count:2d} 条目 ({perfect_count/non_empty_count*100:.1f}%)")
    print(f"  优秀 (偏差 ≤ 10%):    {excellent_count:2d} 条目 ({excellent_count/non_empty_count*100:.1f}%)")
    print(f"  良好 (偏差 ≤ 15%):    {good_count:2d} 条目 ({good_count/non_empty_count*100:.1f}%)")
    print(f"  可接受 (偏差 ≤ 20%):  {acceptable_count:2d} 条目 ({acceptable_count/non_empty_count*100:.1f}%)")
    print(f"  需要改进 (偏差 > 20%): {needs_improvement:2d} 条目 ({needs_improvement/non_empty_count*100:.1f}%)")
    
    print(f"\n总体统计:")
    print(f"  平均偏差: {avg_deviation:.1f}%")
    print(f"  达标率 (偏差 ≤ 15%): {(perfect_count + excellent_count + good_count)/non_empty_count*100:.1f}%")
    print(f"  基本达标率 (偏差 ≤ 20%): {(non_empty_count - needs_improvement)/non_empty_count*100:.1f}%")
    
    if problem_items:
        print(f"\n仍需改进的条目 (偏差 > 20%):")
        problem_items.sort(key=lambda x: x['deviation'], reverse=True)
        for item in problem_items[:10]:  # 只显示前10个最严重的
            print(f"  {item['id']}: {item['actual']}/{item['required']} 字 (偏差 {item['deviation']:.1f}%)")
        
        if len(problem_items) > 10:
            print(f"  ... 还有 {len(problem_items) - 10} 个条目需要改进")
    
    print(f"\n修复建议:")
    if needs_improvement > 0:
        print(f"  - 还有 {needs_improvement} 个条目字数偏差超过20%，建议继续扩展")
        print(f"  - 可以继续运行 smart_content_expander.py 进行进一步优化")
    else:
        print(f"  - 所有条目字数偏差都在20%以内，质量良好")
    
    if avg_deviation > 15:
        print(f"  - 平均偏差较高，建议整体优化")
    elif avg_deviation > 10:
        print(f"  - 平均偏差适中，可以进行精细调整")
    else:
        print(f"  - 平均偏差较低，质量优秀")
    
    print("=" * 80)
    
    return {
        'total_items': non_empty_count,
        'perfect': perfect_count,
        'excellent': excellent_count,
        'good': good_count,
        'acceptable': acceptable_count,
        'needs_improvement': needs_improvement,
        'avg_deviation': avg_deviation,
        'problem_items': len(problem_items)
    }

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python final_word_count_report.py <json_file>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    report = generate_final_report(file_path)
