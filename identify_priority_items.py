import json
import sys

def count_words(text):
    """计算字数"""
    if not text:
        return 0
    return len(text.strip().split())

def identify_priority_items(file_path):
    """识别需要优先扩展的条目"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    shortfall_items = []
    
    for i, item in enumerate(data):
        if item['answer'].strip():
            actual_words = count_words(item['answer'])
            required_words = item['word_limit']
            shortfall = required_words - actual_words
            shortfall_percent = (shortfall / required_words) * 100
            
            if shortfall > 0:
                shortfall_items.append({
                    'index': i,
                    'id': item['id'],
                    'question': item['question'],
                    'type': item['type'],
                    'actual': actual_words,
                    'required': required_words,
                    'shortfall': shortfall,
                    'shortfall_percent': shortfall_percent
                })
    
    # 按字数不足百分比排序
    shortfall_items.sort(key=lambda x: x['shortfall_percent'], reverse=True)
    
    # 分类
    critical_items = [item for item in shortfall_items if item['shortfall_percent'] > 50]
    high_priority = [item for item in shortfall_items if 40 <= item['shortfall_percent'] <= 50]
    medium_priority = [item for item in shortfall_items if 20 <= item['shortfall_percent'] < 40]
    low_priority = [item for item in shortfall_items if item['shortfall_percent'] < 20]
    
    print("字数不足分析报告")
    print("=" * 60)
    print(f"总条目数: {len(data)}")
    print(f"字数不足条目: {len(shortfall_items)}")
    print(f"  - 严重不足 (>50%): {len(critical_items)}")
    print(f"  - 高度不足 (40-50%): {len(high_priority)}")
    print(f"  - 中度不足 (20-40%): {len(medium_priority)}")
    print(f"  - 轻度不足 (<20%): {len(low_priority)}")
    
    print(f"\n严重不足条目 (>50%):")
    for item in critical_items:
        print(f"  {item['id']}: {item['actual']}/{item['required']} ({item['shortfall_percent']:.1f}%)")
        print(f"    题目: {item['question'][:60]}...")
    
    print(f"\n高度不足条目 (40-50%):")
    for item in high_priority[:10]:  # 只显示前10个
        print(f"  {item['id']}: {item['actual']}/{item['required']} ({item['shortfall_percent']:.1f}%)")
    
    return critical_items, high_priority, medium_priority, low_priority

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python identify_priority_items.py <json_file>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    critical, high, medium, low = identify_priority_items(file_path)
