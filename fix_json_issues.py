#!/usr/bin/env python3
"""
JSON修复脚本
自动修复 gemini_2.5pro_25_08_27_10_prompt.json 文件中的问题
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any

def load_json_file(file_path: str) -> List[Dict[str, Any]]:
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {file_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ 错误: JSON格式无效 - {e}")
        sys.exit(1)

def save_json_file(file_path: str, data: List[Dict[str, Any]]) -> None:
    """保存JSON文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        print(f"✅ 已保存修复后的文件: {file_path}")
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        sys.exit(1)

def fix_id_format(data: List[Dict[str, Any]]) -> int:
    """修复ID格式问题"""
    fixes = 0
    for item in data:
        if "id" in item:
            original_id = item["id"]
            # 修复 smp25_pre_40 -> smp25_pre_040 这类问题
            if original_id.startswith("smp25_pre_") and len(original_id.split("_")[-1]) < 3:
                number_part = original_id.split("_")[-1]
                if number_part.isdigit():
                    new_id = f"smp25_pre_{int(number_part):03d}"
                    if new_id != original_id:
                        print(f"🔧 修复ID: {original_id} -> {new_id}")
                        item["id"] = new_id
                        fixes += 1
    return fixes

def count_words(text: str) -> int:
    """简单的单词计数"""
    if not text:
        return 0
    words = text.strip().split()
    return len(words)

def truncate_answer_to_limit(answer: str, word_limit: int) -> str:
    """将答案截断到字数限制"""
    if not answer:
        return answer
    
    words = answer.strip().split()
    if len(words) <= word_limit:
        return answer
    
    # 截断到字数限制
    truncated_words = words[:word_limit]
    truncated_text = " ".join(truncated_words)
    
    # 尝试在句子结尾截断，避免截断到句子中间
    sentences_endings = ['.', '!', '?', '。', '！', '？']
    
    # 从后往前找最近的句子结尾
    for i in range(len(truncated_text) - 1, max(0, len(truncated_text) - 100), -1):
        if truncated_text[i] in sentences_endings:
            # 检查截断后的字数是否还在合理范围内（至少90%的限制）
            truncated_at_sentence = truncated_text[:i+1]
            if count_words(truncated_at_sentence) >= word_limit * 0.9:
                return truncated_at_sentence
    
    # 如果找不到合适的句子结尾，就直接截断
    return truncated_text

def fix_word_limits(data: List[Dict[str, Any]]) -> int:
    """修复字数超限问题"""
    fixes = 0
    for item in data:
        answer = item.get("answer", "")
        word_limit = item.get("word_limit", 0)
        word_count = count_words(answer)
        
        if word_count > word_limit:
            print(f"🔧 修复字数超限: ID {item.get('id')}, {word_count} -> {word_limit} 字")
            item["answer"] = truncate_answer_to_limit(answer, word_limit)
            fixes += 1
    
    return fixes

def create_backup(file_path: str) -> str:
    """创建备份文件"""
    backup_path = file_path.replace('.json', '_backup.json')
    try:
        with open(file_path, 'r', encoding='utf-8') as src:
            with open(backup_path, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
        print(f"📋 已创建备份文件: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"⚠️  创建备份失败: {e}")
        return ""

def main():
    """主函数"""
    print("🔧 开始修复JSON文件...")
    print("=" * 60)
    
    # 文件路径
    answer_file = "results/gemini_2.5pro_25_08_27_10_prompt.json"
    
    # 创建备份
    backup_file = create_backup(answer_file)
    
    # 加载文件
    print(f"📁 加载文件: {answer_file}")
    data = load_json_file(answer_file)
    print(f"✅ 成功加载 {len(data)} 条记录")
    print()
    
    total_fixes = 0
    
    # 修复ID格式
    print("🔧 修复ID格式...")
    id_fixes = fix_id_format(data)
    total_fixes += id_fixes
    print(f"✅ 修复了 {id_fixes} 个ID格式问题")
    print()
    
    # 修复字数超限
    print("🔧 修复字数超限...")
    word_fixes = fix_word_limits(data)
    total_fixes += word_fixes
    print(f"✅ 修复了 {word_fixes} 个字数超限问题")
    print()
    
    # 保存修复后的文件
    if total_fixes > 0:
        save_json_file(answer_file, data)
        print(f"🎉 总共修复了 {total_fixes} 个问题")
        print(f"📋 原文件已备份为: {backup_file}")
        print("\n建议运行 python test_json_validation.py 重新验证")
    else:
        print("✅ 没有发现需要修复的问题")

if __name__ == "__main__":
    main()
