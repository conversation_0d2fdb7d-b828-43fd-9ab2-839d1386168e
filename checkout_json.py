import json
import sys
import os

def count_words(text):
    """
    计算以空格分隔的单词数
    """
    if not text:
        return 0
    
    # 去除首尾空白字符
    text = text.strip()
    
    # 使用split()方法分割单词，这是最标准的以空格分隔的字符序列计算方法
    words = text.split()
    return len(words)

def check_json_format(file_path):
    """
    检查JSON文件格式是否正确
    """
    print(f"正在检查文件: {file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return False
    
    # 尝试解析JSON
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        print(f"错误: JSON格式不正确 - {e}")
        return False
    except Exception as e:
        print(f"错误: 读取文件时出现问题 - {e}")
        return False
    
    # 检查根元素是否为列表
    if not isinstance(data, list):
        print("错误: JSON根元素应该是一个数组")
        return False
    
    print(f"JSON文件包含 {len(data)} 个条目")
    
    # 检查每个条目
    for i, item in enumerate(data):
        if not isinstance(item, dict):
            print(f"错误: 条目 {i} 应该是一个对象")
            return False
            
        # 检查必需字段
        required_fields = ['id', 'question', 'type', 'word_limit', 'answer']
        for field in required_fields:
            if field not in item:
                print(f"错误: 条目 {i} 缺少必需字段 '{field}'")
                return False
        
        # 检查字段类型
        if not isinstance(item['id'], str):
            print(f"错误: 条目 {i} 的 'id' 字段应该是一个字符串")
            return False
            
        if not isinstance(item['question'], str):
            print(f"错误: 条目 {i} 的 'question' 字段应该是一个字符串")
            return False
            
        if not isinstance(item['type'], str):
            print(f"错误: 条目 {i} 的 'type' 字段应该是一个字符串")
            return False
            
        if not isinstance(item['word_limit'], int):
            print(f"错误: 条目 {i} 的 'word_limit' 字段应该是一个整数")
            return False
            
        if not isinstance(item['answer'], str):
            print(f"错误: 条目 {i} 的 'answer' 字段应该是一个字符串")
            return False
    
    print("JSON格式检查通过!")
    return True

def check_content_quality(file_path):
    """
    检查内容质量
    """
    print("\n正在检查内容质量...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    issues = []
    
    for i, item in enumerate(data):
        # 检查answer是否为空
        if not item['answer'].strip():
            issues.append(f"警告: 条目 {i} (ID: {item['id']}) 的 'answer' 字段为空")
        
        # 检查answer长度是否大致符合word_limit
        answer_words = count_words(item['answer'])
        expected_words = item['word_limit']
        
        if item['answer'].strip():  # 只对非空answer检查
            # 计算偏差百分比
            if expected_words > 0:  # 避免除零错误
                deviation = abs(answer_words - expected_words) / expected_words * 100
            else:
                deviation = 100 if answer_words > 0 else 0
            
            # 如果偏差超过10%，则发出警告
            if deviation > 10:
                issues.append(f"注意: 条目 {i} (ID: {item['id']}) 的回答字数 ({answer_words}) 与要求字数 ({expected_words}) 偏差 {deviation:.1f}%")
    
    if issues:
        print("发现以下问题:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("内容质量检查通过!")
    
    return len(issues) == 0

def main():
    if len(sys.argv) < 2:
        print("用法: python checkout_json.py <json_file>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    # 执行检查
    format_ok = check_json_format(file_path)
    
    if format_ok:
        check_content_quality(file_path)
    else:
        print("JSON格式检查失败，跳过内容质量检查")
        sys.exit(1)

if __name__ == "__main__":
    main()