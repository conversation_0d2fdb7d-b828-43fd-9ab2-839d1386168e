# Qwen API 配置文件
# 请在这里设置你的Qwen API密钥和其他配置

# 方法1: 直接在这里设置API密钥（不推荐，因为会暴露在代码中）
QWEN_API_KEY = "your-qwen-api-key-here"

# 方法2: 使用环境变量（推荐）
# 在命令行中设置: set QWEN_API_KEY=your-actual-api-key
# 或在 .env 文件中设置

# Qwen API 配置
QWEN_CONFIG = {
    "base_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
    "model": "qwen-plus",  # 可选: qwen-max, qwen-turbo, qwen-plus
    "max_tokens": 600,
    "temperature": 0.7,
    "top_p": 0.8,
    "timeout": 30
}

# 扩展内容的质量控制参数
EXPANSION_CONFIG = {
    "min_expansion_words": 50,      # 最小扩展字数
    "max_expansion_words": 300,     # 最大扩展字数
    "target_accuracy": 0.9,         # 目标准确度（90%）
    "max_retries": 3,               # API调用最大重试次数
    "retry_delay": 2                # 重试延迟（秒）
}
