import json
import sys
import re

def count_words_simple(text):
    """
    简单的空格分隔字数统计 (当前checkout_json.py使用的方法)
    """
    if not text:
        return 0
    return len(text.strip().split())

def count_words_regex(text):
    """
    使用正则表达式的字数统计，更准确地处理标点符号
    """
    if not text:
        return 0
    # 使用正则表达式匹配单词（字母数字序列）
    words = re.findall(r'\b\w+\b', text)
    return len(words)

def count_words_detailed(text):
    """
    详细的字数统计分析
    """
    if not text:
        return {
            'simple_split': 0,
            'regex_words': 0,
            'characters': 0,
            'characters_no_spaces': 0
        }
    
    # 多种计数方法
    simple_split = len(text.strip().split())
    regex_words = len(re.findall(r'\b\w+\b', text))
    characters = len(text)
    characters_no_spaces = len(text.replace(' ', ''))
    
    return {
        'simple_split': simple_split,
        'regex_words': regex_words,
        'characters': characters,
        'characters_no_spaces': characters_no_spaces
    }

def analyze_word_count_discrepancies(file_path):
    """
    分析字数统计差异
    """
    print(f"正在分析文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"文件包含 {len(data)} 个条目\n")
    
    # 分析前10个条目的详细字数统计
    print("详细字数分析 (前10个条目):")
    print("=" * 80)
    
    for i in range(min(10, len(data))):
        item = data[i]
        answer = item['answer']
        expected_words = item['word_limit']
        
        counts = count_words_detailed(answer)
        
        print(f"\n条目 {i} (ID: {item['id']}):")
        print(f"  要求字数: {expected_words}")
        print(f"  简单分割: {counts['simple_split']} 字")
        print(f"  正则匹配: {counts['regex_words']} 字")
        print(f"  总字符数: {counts['characters']} 字符")
        print(f"  无空格字符: {counts['characters_no_spaces']} 字符")
        
        # 计算偏差
        simple_deviation = abs(counts['simple_split'] - expected_words) / expected_words * 100
        regex_deviation = abs(counts['regex_words'] - expected_words) / expected_words * 100
        
        print(f"  简单分割偏差: {simple_deviation:.1f}%")
        print(f"  正则匹配偏差: {regex_deviation:.1f}%")
        
        # 显示答案的前100个字符
        print(f"  答案开头: {answer[:100]}...")
    
    # 统计所有条目的偏差情况
    print("\n\n全部条目偏差统计:")
    print("=" * 80)
    
    large_deviations = []
    total_simple_deviation = 0
    total_regex_deviation = 0
    
    for i, item in enumerate(data):
        answer = item['answer']
        expected_words = item['word_limit']
        
        if answer.strip():  # 只分析非空答案
            simple_count = count_words_simple(answer)
            regex_count = count_words_regex(answer)
            
            simple_deviation = abs(simple_count - expected_words) / expected_words * 100
            regex_deviation = abs(regex_count - expected_words) / expected_words * 100
            
            total_simple_deviation += simple_deviation
            total_regex_deviation += regex_deviation
            
            if simple_deviation > 10:
                large_deviations.append({
                    'index': i,
                    'id': item['id'],
                    'expected': expected_words,
                    'simple_count': simple_count,
                    'regex_count': regex_count,
                    'simple_deviation': simple_deviation,
                    'regex_deviation': regex_deviation
                })
    
    non_empty_count = len([item for item in data if item['answer'].strip()])
    avg_simple_deviation = total_simple_deviation / non_empty_count if non_empty_count > 0 else 0
    avg_regex_deviation = total_regex_deviation / non_empty_count if non_empty_count > 0 else 0
    
    print(f"非空答案数量: {non_empty_count}")
    print(f"平均简单分割偏差: {avg_simple_deviation:.1f}%")
    print(f"平均正则匹配偏差: {avg_regex_deviation:.1f}%")
    print(f"偏差>10%的条目数量: {len(large_deviations)}")
    
    # 显示偏差最大的几个条目
    if large_deviations:
        print(f"\n偏差最大的前5个条目:")
        large_deviations.sort(key=lambda x: x['simple_deviation'], reverse=True)
        for item in large_deviations[:5]:
            print(f"  条目 {item['index']} (ID: {item['id']}):")
            print(f"    要求: {item['expected']}, 简单分割: {item['simple_count']}, 正则匹配: {item['regex_count']}")
            print(f"    简单偏差: {item['simple_deviation']:.1f}%, 正则偏差: {item['regex_deviation']:.1f}%")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python detailed_word_count_analysis.py <json_file>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    analyze_word_count_discrepancies(file_path)
