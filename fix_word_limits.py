import json
import sys
import os
import shutil

def fix_word_limits(example_file, submission_file, output_file=None):
    """
    修复提交文件中的字数限制，使其与示例文件一致
    """
    print("正在修复字数限制...")
    
    # 读取示例文件
    with open(example_file, 'r', encoding='utf-8') as f:
        example_data = json.load(f)
    
    # 读取提交文件
    with open(submission_file, 'r', encoding='utf-8') as f:
        submission_data = json.load(f)
    
    # 创建字典以便快速查找
    example_dict = {item['id']: item for item in example_data}
    
    # 备份原文件
    if output_file is None:
        backup_file = submission_file + '.backup'
        shutil.copy2(submission_file, backup_file)
        print(f"已创建备份文件: {backup_file}")
        output_file = submission_file
    
    # 修复字数限制
    fixes_made = 0
    for item in submission_data:
        item_id = item['id']
        if item_id in example_dict:
            correct_limit = example_dict[item_id]['word_limit']
            if item['word_limit'] != correct_limit:
                print(f"修复 {item_id}: {item['word_limit']} -> {correct_limit}")
                item['word_limit'] = correct_limit
                fixes_made += 1
    
    # 保存修复后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(submission_data, f, ensure_ascii=False, indent=4)
    
    print(f"\n修复完成! 共修复了 {fixes_made} 个条目的字数限制")
    print(f"修复后的文件已保存为: {output_file}")
    
    return fixes_made

def count_words(text):
    """
    计算字数
    """
    if not text:
        return 0
    return len(text.strip().split())

def analyze_word_shortfall(file_path):
    """
    分析字数不足的问题
    """
    print(f"\n正在分析字数不足问题...")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    shortfall_issues = []
    total_shortfall = 0
    
    for i, item in enumerate(data):
        if item['answer'].strip():
            actual_words = count_words(item['answer'])
            required_words = item['word_limit']
            shortfall = required_words - actual_words
            shortfall_percent = (shortfall / required_words) * 100
            
            if shortfall > 0:
                shortfall_issues.append({
                    'index': i,
                    'id': item['id'],
                    'actual': actual_words,
                    'required': required_words,
                    'shortfall': shortfall,
                    'shortfall_percent': shortfall_percent
                })
                total_shortfall += shortfall
    
    print(f"发现 {len(shortfall_issues)} 个条目字数不足")
    print(f"总计缺少字数: {total_shortfall}")
    
    # 显示字数不足最严重的10个条目
    if shortfall_issues:
        shortfall_issues.sort(key=lambda x: x['shortfall_percent'], reverse=True)
        print(f"\n字数不足最严重的10个条目:")
        for issue in shortfall_issues[:10]:
            print(f"  {issue['id']}: 实际 {issue['actual']} / 要求 {issue['required']} "
                  f"(缺少 {issue['shortfall']} 字, {issue['shortfall_percent']:.1f}%)")
    
    return shortfall_issues

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("用法: python fix_word_limits.py <example_file> <submission_file> [output_file]")
        sys.exit(1)
    
    example_file = sys.argv[1]
    submission_file = sys.argv[2]
    output_file = sys.argv[3] if len(sys.argv) > 3 else None
    
    # 修复字数限制
    fixes_made = fix_word_limits(example_file, submission_file, output_file)
    
    # 分析字数不足问题
    final_file = output_file if output_file else submission_file
    shortfall_issues = analyze_word_shortfall(final_file)
    
    print(f"\n总结:")
    print(f"- 修复了 {fixes_made} 个字数限制错误")
    print(f"- 发现 {len(shortfall_issues)} 个条目字数不足")
    print(f"- 建议检查答案内容，确保达到要求的字数")
